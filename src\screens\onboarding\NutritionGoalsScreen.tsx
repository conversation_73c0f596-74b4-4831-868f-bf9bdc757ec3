import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Text } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import Animated, { SlideInLeft } from 'react-native-reanimated';

import { OnboardingStackParamList } from './OnboardingNavigator';
import { useOnboarding } from '../../contexts/OnboardingContext';
import OnboardingLayout from '../../components/onboarding/OnboardingLayout';
import { ModernInput } from '../../components/onboarding/ModernInput';

type NutritionGoalsScreenNavigationProp = StackNavigationProp<OnboardingStackParamList, 'NutritionGoals'>;

const NutritionGoalsScreen: React.FC = () => {
  const navigation = useNavigation<NutritionGoalsScreenNavigationProp>();
  const { data, updateData, nextStep, previousStep } = useOnboarding();

  const [localData, setLocalData] = useState({
    caloriesGoal: data.caloriesGoal.toString(),
    proteinGoal: data.proteinGoal.toString(),
    carbsPercentage: data.carbsPercentage.toString(),
    fatPercentage: data.fatPercentage.toString(),
  });

  // Calculate recommended calories based on user data
  useEffect(() => {
    if (data.weight && data.height && data.age && data.gender && data.activityLevel) {
      const bmr = calculateBMR();
      const tdee = calculateTDEE(bmr);
      const adjustedCalories = adjustForGoal(tdee);
      
      setLocalData(prev => ({
        ...prev,
        caloriesGoal: Math.round(adjustedCalories).toString(),
        proteinGoal: Math.round(adjustedCalories * 0.3 / 4).toString(), // 30% of calories from protein
      }));
    }
  }, [data]);

  const calculateBMR = () => {
    // Mifflin-St Jeor Equation
    if (data.gender === 'male') {
      return 10 * data.weight + 6.25 * data.height - 5 * data.age + 5;
    } else {
      return 10 * data.weight + 6.25 * data.height - 5 * data.age - 161;
    }
  };

  const calculateTDEE = (bmr: number) => {
    const activityMultipliers = {
      sedentary: 1.2,
      lightly_active: 1.375,
      moderately_active: 1.55,
      very_active: 1.725,
    };
    return bmr * (activityMultipliers[data.activityLevel as keyof typeof activityMultipliers] || 1.55);
  };

  const adjustForGoal = (tdee: number) => {
    switch (data.weightGoal) {
      case 'lose':
        // Calculate deficit based on weight loss goal and timeframe
        const weightToLose = data.weight - data.targetWeight; // kg to lose
        const weeksToLose = data.timeframe || 12; // weeks
        const weeklyWeightLoss = weightToLose / weeksToLose; // kg per week

        // Safe weight loss: 0.5-1 kg per week (1-2 lbs)
        // 1 kg fat = ~7700 calories, so daily deficit = (weekly loss * 7700) / 7
        const safeWeeklyLoss = Math.min(Math.max(weeklyWeightLoss, 0.25), 1.0); // Clamp between 0.25-1 kg/week
        const dailyDeficit = (safeWeeklyLoss * 7700) / 7; // Convert to daily calorie deficit

        return Math.max(tdee - dailyDeficit, 1200); // Never go below 1200 calories
      case 'gain':
        // Calculate surplus based on weight gain goal and timeframe
        const weightToGain = data.targetWeight - data.weight; // kg to gain
        const weeksToGain = data.timeframe || 12; // weeks
        const weeklyWeightGain = weightToGain / weeksToGain; // kg per week

        // Safe weight gain: 0.25-0.5 kg per week
        const safeWeeklyGain = Math.min(Math.max(weeklyWeightGain, 0.25), 0.5); // Clamp between 0.25-0.5 kg/week
        const dailySurplus = (safeWeeklyGain * 7700) / 7; // Convert to daily calorie surplus

        return tdee + dailySurplus;
      default:
        return tdee; // maintain
    }
  };

  const updateLocalData = (field: string, value: string) => {
    setLocalData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleNext = () => {
    updateData('caloriesGoal', parseInt(localData.caloriesGoal) || 2000);
    updateData('proteinGoal', parseInt(localData.proteinGoal) || 150);
    updateData('carbsPercentage', parseInt(localData.carbsPercentage) || 45);
    updateData('fatPercentage', parseInt(localData.fatPercentage) || 30);
    
    nextStep();
    navigation.navigate('NotificationSettings');
  };

  const handleBack = () => {
    previousStep();
    navigation.goBack();
  };

  const isFormValid = () => {
    const calories = parseInt(localData.caloriesGoal);
    const protein = parseInt(localData.proteinGoal);
    const carbs = parseInt(localData.carbsPercentage);
    const fat = parseInt(localData.fatPercentage);
    
    return calories >= 1200 && calories <= 4000 &&
           protein >= 50 && protein <= 300 &&
           carbs >= 20 && carbs <= 70 &&
           fat >= 15 && fat <= 50 &&
           (carbs + fat) <= 85; // Leave room for protein
  };

  return (
    <OnboardingLayout
      title="Nutrition Goals"
      subtitle="Set your daily nutrition targets"
      onNext={handleNext}
      onBack={handleBack}
      nextDisabled={!isFormValid()}
      showSkip={true}
      onSkip={handleNext}
    >
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.formContainer}>
          {/* Calculated Goals Display */}
          <Animated.View entering={SlideInLeft.delay(200).duration(600)}>
            <View style={styles.calculatedGoalsCard}>
              <Text style={styles.calculatedTitle}>Your Personalized Nutrition Goals</Text>
              <Text style={styles.calculatedSubtitle}>
                Based on your {data.weightGoal === 'lose' ? 'weight loss' : data.weightGoal === 'gain' ? 'weight gain' : 'maintenance'} goal
              </Text>

              <View style={styles.goalRow}>
                <View style={styles.goalIcon}>
                  <Text style={styles.goalIconText}>🔥</Text>
                </View>
                <View style={styles.goalContent}>
                  <Text style={styles.goalLabel}>Daily Calories</Text>
                  <Text style={styles.goalValue}>{localData.caloriesGoal} cal</Text>
                </View>
              </View>

              <View style={styles.goalRow}>
                <View style={styles.goalIcon}>
                  <Text style={styles.goalIconText}>💪</Text>
                </View>
                <View style={styles.goalContent}>
                  <Text style={styles.goalLabel}>Daily Protein</Text>
                  <Text style={styles.goalValue}>{localData.proteinGoal} g</Text>
                </View>
              </View>

              {data.weightGoal === 'lose' && (
                <View style={styles.deficitInfo}>
                  <Text style={styles.deficitText}>
                    🎯 Calculated for safe weight loss of {((data.weight - data.targetWeight) / (data.timeframe || 12)).toFixed(1)} kg/week
                  </Text>
                </View>
              )}

              {data.weightGoal === 'gain' && (
                <View style={styles.deficitInfo}>
                  <Text style={styles.deficitText}>
                    🎯 Calculated for healthy weight gain of {((data.targetWeight - data.weight) / (data.timeframe || 12)).toFixed(1)} kg/week
                  </Text>
                </View>
              )}
            </View>
          </Animated.View>

          <Animated.View entering={SlideInLeft.delay(400).duration(600)}>
            <Text style={styles.sectionTitle}>Macronutrient Distribution</Text>
          </Animated.View>

          <Animated.View entering={SlideInLeft.delay(500).duration(600)}>
            <ModernInput
              label="Carbohydrates"
              value={localData.carbsPercentage}
              onChangeText={(text) => updateLocalData('carbsPercentage', text.replace(/[^0-9]/g, ''))}
              placeholder="45"
              keyboardType="numeric"
              icon="nutrition"
              suffix="%"
              maxLength={2}
            />
          </Animated.View>

          <Animated.View entering={SlideInLeft.delay(600).duration(600)}>
            <ModernInput
              label="Fats"
              value={localData.fatPercentage}
              onChangeText={(text) => updateLocalData('fatPercentage', text.replace(/[^0-9]/g, ''))}
              placeholder="30"
              keyboardType="numeric"
              icon="water"
              suffix="%"
              maxLength={2}
            />
          </Animated.View>

          <Animated.View entering={SlideInLeft.delay(700).duration(600)}>
            <View style={styles.infoCard}>
              <Text style={styles.infoText}>
                Protein will make up the remaining percentage. 
                Recommended: 45% carbs, 30% fat, 25% protein.
              </Text>
            </View>
          </Animated.View>
        </View>
      </ScrollView>
    </OnboardingLayout>
  );
};

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
  },
  formContainer: {
    paddingTop: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1F2937',
    marginBottom: 16,
    marginTop: 8,
    textAlign: 'center',
    letterSpacing: 0.3,
  },
  infoCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    padding: 16,
    marginTop: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  infoText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 20,
  },
  calculatedGoalsCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 24,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  calculatedTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1F2937',
    textAlign: 'center',
    marginBottom: 8,
  },
  calculatedSubtitle: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 24,
  },
  goalRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    paddingVertical: 8,
  },
  goalIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#F0FDF4',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  goalIconText: {
    fontSize: 20,
  },
  goalContent: {
    flex: 1,
  },
  goalLabel: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 4,
  },
  goalValue: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
  },
  deficitInfo: {
    backgroundColor: '#F0FDF4',
    borderRadius: 12,
    padding: 16,
    marginTop: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#22C55E',
  },
  deficitText: {
    fontSize: 14,
    color: '#15803D',
    textAlign: 'center',
    fontWeight: '500',
  },
});

export default NutritionGoalsScreen;
