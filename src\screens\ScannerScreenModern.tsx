import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Alert,
  ImageBackground,
  StatusBar,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import Animated, {
  FadeInUp,
  FadeInDown,
  SlideInLeft,
  SlideInRight,
  SlideInUp,
  ZoomIn,
  BounceIn,
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withRepeat,
  withSequence,
  withTiming,
  interpolate,
  Extrapolate,
} from 'react-native-reanimated';

import { Colors, Spacing, BorderRadius, FontSizes, FontWeights } from '../constants/Colors';
import { ModernButton } from '../components/ModernButton';
import LottieIcon from '../components/LottieIcon';
import { CircularProgress } from '../components/CircularProgress';

import * as Haptics from 'expo-haptics';
import ApiService, { MealAnalysis } from '../services/ApiService';
import { useProfile } from '../contexts/ProfileContext';
import DatabaseIntegrationService from '../services/DatabaseIntegrationService';
import ErrorHandlingService from '../services/ErrorHandlingService';
import WeightGoalTracker from '../services/WeightGoalTracker';

// Get screen dimensions for responsive design
const { width: screenWidth, height: screenHeight } = Dimensions.get('window');
const isSmallScreen = screenWidth < 375;
const isMediumScreen = screenWidth >= 375 && screenWidth < 414;
const isLargeScreen = screenWidth >= 414;

const ScannerScreenModern: React.FC = () => {
  const { addRecentMeal } = useProfile();
  const dbService = DatabaseIntegrationService;
  const [image, setImage] = useState<string | null>(null);
  const [analysis, setAnalysis] = useState<MealAnalysis | null>(null);
  const [loading, setLoading] = useState(false);
  const [scanProgress, setScanProgress] = useState(0);

  // Animated values for micro-interactions
  const pulseValue = useSharedValue(1);
  const scanningValue = useSharedValue(0);
  const progressValue = useSharedValue(0);
  const cameraIconScale = useSharedValue(1);

  // Initialize animations
  React.useEffect(() => {
    // Pulse animation for camera icon
    pulseValue.value = withRepeat(
      withSequence(
        withSpring(1.1, { duration: 1500 }),
        withSpring(1, { duration: 1500 })
      ),
      -1,
      true
    );

    // Camera icon breathing effect
    cameraIconScale.value = withRepeat(
      withSequence(
        withTiming(1.05, { duration: 2000 }),
        withTiming(1, { duration: 2000 })
      ),
      -1,
      true
    );

    return () => {
      // Clean up animations to prevent memory leaks
      pulseValue.value = withSpring(1);
      cameraIconScale.value = withSpring(1);
      scanningValue.value = withSpring(0);
      progressValue.value = withSpring(0);
    };
  }, []);

  // Animated styles
  const pulseStyle = useAnimatedStyle(() => ({
    transform: [{ scale: pulseValue.value }],
  }));

  const scanningStyle = useAnimatedStyle(() => ({
    opacity: interpolate(scanningValue.value, [0, 1], [0.3, 1], Extrapolate.CLAMP),
    transform: [
      {
        rotate: `${interpolate(scanningValue.value, [0, 1], [0, 360], Extrapolate.CLAMP)}deg`
      }
    ],
  }));

  const cameraIconStyle = useAnimatedStyle(() => ({
    transform: [{ scale: cameraIconScale.value }],
  }));

  const progressStyle = useAnimatedStyle(() => ({
    width: `${interpolate(progressValue.value, [0, 1], [0, 100], Extrapolate.CLAMP)}%`,
  }));

  const pickImage = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permission needed', 'Please grant camera roll permissions to use this feature.');
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });

    if (!result.canceled) {
      setImage(result.assets[0].uri);
      setAnalysis(null);
    }
  };

  const takePhoto = async () => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permission needed', 'Please grant camera permissions to use this feature.');
      return;
    }

    const result = await ImagePicker.launchCameraAsync({
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });

    if (!result.canceled) {
      setImage(result.assets[0].uri);
      setAnalysis(null);
    }
  };



  const analyzeImage = async () => {
    if (!image) {
      Alert.alert('No Image', 'Please take a photo first');
      return;
    }

    // Check network connectivity first
    const isConnected = await ErrorHandlingService.checkNetworkConnectivity();
    if (!isConnected) {
      Alert.alert(
        'No Internet Connection',
        'Please check your internet connection and try again.',
        [{ text: 'OK', style: 'default' }]
      );
      return;
    }

    setLoading(true);
    setScanProgress(0);

    // Start scanning animation
    scanningValue.value = withRepeat(
      withTiming(1, { duration: 1000 }),
      -1,
      false
    );

    // Animate progress bar
    progressValue.value = withTiming(1, { duration: 3000 });

    // Simulate progress updates
    const progressInterval = setInterval(() => {
      setScanProgress(prev => {
        if (prev >= 100) {
          clearInterval(progressInterval);
          return 100;
        }
        return prev + Math.random() * 15;
      });
    }, 200);

    try {
      console.log('🔍 Starting API call to scanMeal...');
      console.log('🔍 Image URI length:', image?.length);
      console.log('🔍 Network connectivity test...');

      // Test basic network connectivity first
      try {
        const testResponse = await fetch('https://www.google.com', {
          method: 'HEAD'
        });
        console.log('✅ Network connectivity OK:', testResponse.status);
      } catch (networkError) {
        console.error('❌ Network connectivity issue:', networkError);
      }

      const result = await ApiService.scanMeal(image);
      console.log('✅ API call successful, result:', {
        mealTitle: result.mealTitle,
        calories: result.calories,
        hasProtein: !!result.totalNutrition?.protein || !!result.macros?.protein,
        proteinValue: result.totalNutrition?.protein || result.macros?.protein,
        fullStructure: Object.keys(result)
      });

      // The result is already in the correct MealAnalysis format from ApiService
      console.log('🔍 Setting analysis result from API:', {
        mealTitle: result.mealTitle,
        hasAnalysis: !!result.analysis,
        hasStrengths: !!result.analysis?.strengths,
        strengthsLength: result.analysis?.strengths?.length,
        hasConcerns: !!result.analysis?.concerns,
        concernsLength: result.analysis?.concerns?.length,
        hasSuggestions: !!result.analysis?.suggestions,
        suggestionsLength: result.analysis?.suggestions?.length
      });
      setAnalysis(result);

      // Sync scan to database
      await dbService.syncScanToDatabase(image, result, true);

      // Save the analyzed meal to recent meals with full nutrition data and scanned image
      const proteinValue = result.totalNutrition?.protein || parseInt(result.macros.protein) || 0;
      const carbsValue = result.totalNutrition?.carbs || parseInt(result.macros.carbs) || 0;
      const fatValue = result.totalNutrition?.fat || parseInt(result.macros.fats) || 0;

      console.log('🔍 Scanner protein extraction:', {
        totalNutritionProtein: result.totalNutrition?.protein,
        macrosProtein: result.macros.protein,
        finalProteinValue: proteinValue,
        resultStructure: Object.keys(result)
      });

      await addRecentMeal({
        name: result.mealTitle,
        time: new Date().toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }),
        calories: result.calories,
        protein: proteinValue,
        carbs: carbsValue,
        fat: fatValue,
        type: 'snack',
        imageUri: image, // Store the scanned image URI
        source: 'scanner' // Mark as scanner-sourced meal
      });

      // Log to WeightGoalTracker for progress tracking - WITH PROTEIN
      const today = new Date().toISOString().split('T')[0];
      await WeightGoalTracker.logDailyNutrition(today, result.calories, proteinValue, 'scanner');

    } catch (error) {
      console.error('❌ API call failed, using fallback data:', error);
      console.error('❌ Error details:', error instanceof Error ? error.message : String(error));
      console.error('❌ Error stack:', error instanceof Error ? error.stack : 'No stack trace');
      // Fallback to mock data using correct MealAnalysis structure
      const fallbackAnalysis = {
        mealTitle: 'Analyzed Meal',
        itemsIdentified: ['Grilled Chicken Breast', 'Brown Rice'],
        calories: 450,
        macros: {
          protein: '25',
          carbs: '35',
          fats: '18'
        },
        healthRating: 8.5,
        analysis: {
          strengths: ['High protein content', 'Balanced macronutrients'],
          concerns: ['Could use more vegetables'],
          suggestions: [
            'Add more leafy greens',
            'Great protein content!'
          ]
        },
        detectedFoods: [
          {
            name: 'Grilled Chicken Breast',
            portion: '4 oz (113g)',
            confidence: 95,
            calories: 250,
            protein: 25,
            carbs: 0,
            fat: 8,
            fiber: 0
          },
          {
            name: 'Brown Rice',
            portion: '1/2 cup (90g)',
            confidence: 90,
            calories: 200,
            protein: 0,
            carbs: 35,
            fat: 10,
            fiber: 8
          }
        ],
        totalNutrition: {
          calories: 450,
          protein: 25,
          carbs: 35,
          fat: 18,
          fiber: 8,
          sugar: 12,
          sodium: 680
        }
      };

      console.log('🔍 Setting fallback analysis:', {
        mealTitle: fallbackAnalysis.mealTitle,
        hasAnalysis: !!fallbackAnalysis.analysis,
        hasStrengths: !!fallbackAnalysis.analysis?.strengths,
        strengthsLength: fallbackAnalysis.analysis?.strengths?.length,
        hasConcerns: !!fallbackAnalysis.analysis?.concerns,
        concernsLength: fallbackAnalysis.analysis?.concerns?.length,
        hasSuggestions: !!fallbackAnalysis.analysis?.suggestions,
        suggestionsLength: fallbackAnalysis.analysis?.suggestions?.length
      });
      setAnalysis(fallbackAnalysis);

      // Save the fallback meal to recent meals too with nutrition data and scanned image
      await addRecentMeal({
        name: fallbackAnalysis.mealTitle,
        time: new Date().toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }),
        calories: fallbackAnalysis.calories,
        protein: fallbackAnalysis.totalNutrition?.protein || parseInt(fallbackAnalysis.macros.protein) || 25, // Fixed: use macros.protein for fallback
        carbs: fallbackAnalysis.totalNutrition?.carbs || parseInt(fallbackAnalysis.macros.carbs) || 35,
        fat: fallbackAnalysis.totalNutrition?.fat || parseInt(fallbackAnalysis.macros.fats) || 18,
        type: 'snack',
        imageUri: image, // Store the scanned image URI even for fallback
        source: 'scanner' // Mark as scanner-sourced meal
      });

      // Log to WeightGoalTracker for progress tracking - WITH PROTEIN
      const today = new Date().toISOString().split('T')[0];
      const proteinAmount = fallbackAnalysis.totalNutrition?.protein || parseInt(fallbackAnalysis.macros.protein) || 25; // Fixed: use macros.protein
      await WeightGoalTracker.logDailyNutrition(today, fallbackAnalysis.calories, proteinAmount, 'scanner');
    }

    clearInterval(progressInterval);
    setScanProgress(100);
    setLoading(false);

    // Stop scanning animation
    scanningValue.value = withTiming(0, { duration: 500 });
    progressValue.value = withTiming(0, { duration: 500 });
  };

  const NutrientCard: React.FC<{ label: string; value: string | number; unit: string; icon: string }> = ({ 
    label, 
    value, 
    unit, 
    icon 
  }) => (
    <View style={styles.nutritionItem}>
      <View style={styles.nutritionIconContainer}>
        <Ionicons name={icon as any} size={20} color="#6B7C5A" />
      </View>
      <Text style={styles.nutritionValue}>{value}{unit}</Text>
      <Text style={styles.nutritionLabel}>{label}</Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="transparent" translucent />

      {/* Beautiful Background with New Screens Background Image */}
      <ImageBackground
        source={require('../../assets/screens background.jpg')}
        style={styles.backgroundContainer}
        resizeMode="cover"
      >
        <View style={styles.whiteOverlay} />
      </ImageBackground>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Main Card Container - Inspired by your design */}
        <Animated.View entering={FadeInDown.duration(800)} style={styles.mainCard}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.title}>AI Food Scanner</Text>
            <Text style={styles.subtitle}>Discover nutrition insights instantly</Text>
          </View>

          {/* Enhanced Camera Section with Animations */}
          <Animated.View entering={FadeInUp.delay(300).duration(800)} style={styles.scanSection}>
            {image ? (
              <Animated.View entering={ZoomIn.duration(600)} style={styles.imageWrapper}>
                <Image source={{ uri: image }} style={styles.previewImage} />
                <TouchableOpacity
                  style={styles.removeButton}
                  onPress={() => {
                    setImage(null);
                    setAnalysis(null);
                    setScanProgress(0);
                  }}
                >
                  <Ionicons name="close" size={18} color="#FFFFFF" />
                </TouchableOpacity>

                {/* Premium AR-Style Scanning Overlay */}
                {loading && (
                  <View style={styles.scanningOverlay}>
                    {/* Single Clean Scanning Animation */}
                    <View style={styles.scanningContainer}>
                      <CircularProgress
                        size={80}
                        progress={scanProgress}
                        color="#FFFFFF"
                        backgroundColor="rgba(255, 255, 255, 0.3)"
                        strokeWidth={4}
                        showPercentage={false}
                        animationDuration={300}
                      />
                      <View style={styles.scanningContent}>
                        <LottieIcon
                          name="aiThinking"
                          size={28}
                          color="#FFFFFF"
                          enableHaptics={false}
                        />
                      </View>
                    </View>
                    <Text style={styles.scanningText}>AI Analyzing...</Text>
                  </View>
                )}
              </Animated.View>
            ) : (
              <View style={styles.scanPlaceholder}>
                <Animated.View style={[styles.cameraIconContainer, pulseStyle]}>
                  <Animated.View style={cameraIconStyle}>
                    <Ionicons name="camera" size={64} color="#6B7C5A" />
                  </Animated.View>
                </Animated.View>
                <Text style={styles.readyText}>Ready to scan</Text>
                <Text style={styles.instructionText}>Take a photo or choose from your gallery</Text>
              </View>
            )}
          </Animated.View>

          {/* Enhanced Action Buttons with Single Row Layout */}
          <Animated.View entering={FadeInUp.delay(500).duration(800)} style={styles.buttonContainer}>
            <Animated.View entering={SlideInUp.delay(600).duration(600)} style={styles.singleButtonWrapper}>
              <TouchableOpacity
                style={styles.modernCameraButton}
                onPress={() => {
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
                  takePhoto();
                }}
                activeOpacity={0.9}
              >
                <View style={styles.cameraButtonContent}>
                  <LottieIcon
                    name="scan"
                    size={32}
                    color={Colors.brandForeground}
                    enableHaptics={false}
                  />
                  <Text style={styles.cameraButtonText}>Scan Food</Text>
                  <Text style={styles.cameraButtonSubtext}>AI-powered analysis</Text>
                </View>
              </TouchableOpacity>
            </Animated.View>

            <Animated.View entering={SlideInUp.delay(700).duration(600)} style={styles.singleButtonWrapper}>
              <ModernButton
                title="Choose from Gallery"
                onPress={() => {
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                  pickImage();
                }}
                variant="secondary"
                size="lg"
                icon="images"
                shadowIntensity="medium"
                borderRadius="xl"
                hapticFeedback={false}
              />
            </Animated.View>


          </Animated.View>

          {/* Premium Analyze Button */}
          {image && (
            <Animated.View entering={FadeInUp.delay(700).duration(800)} style={styles.analyzeContainer}>
              <ModernButton
                title={loading ? 'Analyzing with AI...' : 'Analyze with AI'}
                onPress={() => {
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
                  analyzeImage();
                }}
                variant="primary"
                size="xl"
                icon={loading ? undefined : "scan"}
                disabled={loading}
                loading={loading}
                shadowIntensity="strong"
                borderRadius="xl"
                fullWidth={true}
                hapticFeedback={false}
                glowEffect={true}
              />
            </Animated.View>
          )}

          {/* Premium Analysis Results with Modern Cards */}
          {analysis && (
            <Animated.View entering={BounceIn.delay(800).duration(800)} style={styles.resultsSection}>
              <Animated.View entering={FadeInUp.delay(900).duration(600)}>
                <Text style={styles.resultsTitle}>✨ AI Analysis Results</Text>
              </Animated.View>

              {/* Nutrition Stats Grid with Staggered Animation */}
              <View style={styles.nutritionGrid}>
                {[
                  {
                    icon: 'flame',
                    value: analysis.totalNutrition?.calories || analysis.calories,
                    label: 'Calories',
                    color: '#6B7C5A'
                  },
                  {
                    icon: 'fitness',
                    value: `${analysis.totalNutrition?.protein || parseInt(analysis.macros.protein)}g`,
                    label: 'Protein',
                    color: '#6B7C5A'
                  },
                  {
                    icon: 'leaf',
                    value: `${analysis.totalNutrition?.carbs || parseInt(analysis.macros.carbs)}g`,
                    label: 'Carbs',
                    color: '#6B7C5A'
                  },
                  {
                    icon: 'water',
                    lottieIcon: 'avocado',
                    value: `${analysis.totalNutrition?.fat || parseInt(analysis.macros.fats)}g`,
                    label: 'Fat',
                    color: '#FFC107',
                    bgColor: 'rgba(255, 193, 7, 0.1)',
                  },
                ].map((item, index) => (
                  <Animated.View
                    key={item.label}
                    entering={ZoomIn.delay(1000 + index * 150).duration(600)}
                    style={styles.nutritionItem}
                  >
                    <View style={[styles.nutritionCardContent, { backgroundColor: '#FFFFFF' }]}>
                      <LottieIcon
                        name={item.lottieIcon as any || 'heartbeat'}
                        size={28}
                        color={item.color}
                        enableHaptics={false}
                      />
                      <Text style={[styles.nutritionValue, { color: item.color }]}>{item.value}</Text>
                      <Text style={styles.nutritionLabel}>{item.label}</Text>
                    </View>
                  </Animated.View>
                ))}
              </View>

              {/* Enhanced Detected Foods with Detailed Information */}
              <Animated.View entering={SlideInLeft.delay(1200).duration(600)} style={styles.detectedFoodsSection}>
                <Text style={styles.detectedFoodsTitle}>Detected Foods</Text>
                {analysis.detectedFoods ? (
                  analysis.detectedFoods.map((food, index) => (
                    <Animated.View
                      key={index}
                      entering={SlideInRight.delay(1300 + index * 100).duration(500)}
                      style={styles.enhancedFoodItem}
                    >
                      <View style={styles.foodItemHeader}>
                        <Ionicons name="restaurant" size={16} color="#6B7C5A" />
                        <Text style={styles.foodItemName}>{food.name}</Text>
                        <Text style={styles.foodItemPortion}>{food.portion}</Text>
                      </View>
                      <View style={styles.foodItemNutrition}>
                        <Text style={styles.foodItemCalories}>{food.calories} cal</Text>
                        <Text style={styles.foodItemMacros}>P: {food.protein}g | C: {food.carbs}g | F: {food.fat}g</Text>
                      </View>
                      {food.confidence && (
                        <View style={styles.confidenceBar}>
                          <View style={[styles.confidenceFill, { width: `${food.confidence}%` }]} />
                          <Text style={styles.confidenceText}>{food.confidence}% confident</Text>
                        </View>
                      )}
                    </Animated.View>
                  ))
                ) : (
                  analysis.itemsIdentified.map((item, index) => (
                    <Animated.View
                      key={index}
                      entering={SlideInRight.delay(1300 + index * 100).duration(500)}
                      style={styles.foodItem}
                    >
                      <Ionicons name="checkmark-circle" size={16} color="#6B7C5A" />
                      <Text style={styles.foodItemText}>{item}</Text>
                    </Animated.View>
                  ))
                )}
              </Animated.View>

              {/* Enhanced Recommendations with Health Insights */}
              <Animated.View entering={SlideInRight.delay(1400).duration(600)} style={styles.recommendationsSection}>
                <Text style={styles.recommendationsTitle}>Health Insights & Recommendations</Text>

                {/* Health Score */}
                <Animated.View entering={FadeInUp.delay(1450).duration(500)} style={styles.healthScoreContainer}>
                  <Text style={styles.healthScoreLabel}>Health Score</Text>
                  <View style={styles.healthScoreBar}>
                    <View style={[styles.healthScoreFill, { width: `${(analysis.healthRating || 0) * 10}%` }]} />
                  </View>
                  <Text style={styles.healthScoreValue}>{((analysis.healthRating || 0) * 10).toFixed(0)}/100</Text>
                </Animated.View>

                {/* Suggestions */}
                {analysis.analysis.suggestions.map((suggestion, index) => (
                  <Animated.View
                    key={index}
                    entering={SlideInLeft.delay(1500 + index * 100).duration(500)}
                    style={styles.recommendation}
                  >
                    <Ionicons name="bulb" size={16} color="#6B7C5A" />
                    <Text style={styles.recommendationText}>{suggestion}</Text>
                  </Animated.View>
                ))}

                {/* Strengths */}
                {analysis.analysis.strengths.map((strength, index) => (
                  <Animated.View
                    key={`strength-${index}`}
                    entering={SlideInLeft.delay(1600 + index * 100).duration(500)}
                    style={styles.strengthItem}
                  >
                    <Ionicons name="checkmark-circle" size={16} color="#4CAF50" />
                    <Text style={styles.strengthText}>{strength}</Text>
                  </Animated.View>
                ))}

                {/* Concerns */}
                {analysis.analysis.concerns.map((concern, index) => (
                  <Animated.View
                    key={`concern-${index}`}
                    entering={SlideInLeft.delay(1700 + index * 100).duration(500)}
                    style={styles.concernItem}
                  >
                    <Ionicons name="warning" size={16} color="#FF9800" />
                    <Text style={styles.concernText}>{concern}</Text>
                  </Animated.View>
                ))}
              </Animated.View>
            </Animated.View>
          )}
        </Animated.View>

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },

  // Beautiful Background
  backgroundContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  whiteOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.85)',
  },

  scrollView: {
    flex: 1,
    paddingTop: 80, // Consistent with homepage
  },

  // Main Card - Modern Design System
  mainCard: {
    marginHorizontal: 20,
    marginBottom: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 32,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.15,
    shadowRadius: 24,
    elevation: 15,
  },

  // Header
  header: {
    alignItems: 'center',
    marginBottom: 32,
  },
  title: {
    fontSize: 32,
    fontWeight: '700',
    color: '#1a202c',
    marginBottom: 8,
    textAlign: 'center',
    letterSpacing: -0.5,
  },
  subtitle: {
    fontSize: 18,
    color: '#4a5568',
    textAlign: 'center',
    fontWeight: '500',
  },



  // Buttons
  buttonContainer: {
    flexDirection: 'column',
    gap: 16,
    marginBottom: 32,
    paddingHorizontal: 20,
    paddingVertical: 8,
    width: '100%',
    alignItems: 'center',
  },
  singleButtonWrapper: {
    width: '100%',
    maxWidth: 280,
  },
  primaryButton: {
    width: '100%',
    backgroundColor: '#6B7C5A',
    borderRadius: 24,
    paddingVertical: 20,
    paddingHorizontal: 24,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 12,
    shadowColor: '#6B7C5A',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.25,
    shadowRadius: 16,
    elevation: 8,
  },
  primaryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButton: {
    width: '100%',
    backgroundColor: '#FFFFFF',
    borderWidth: 2,
    borderColor: '#6B7C5A',
    borderRadius: 24,
    paddingVertical: 20,
    paddingHorizontal: 24,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 12,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  secondaryButtonText: {
    color: '#6B7C5A',
    fontSize: 16,
    fontWeight: '600',
  },

  // Analyze Button
  analyzeContainer: {
    marginBottom: Spacing.xl,
  },
  analyzeButton: {
    backgroundColor: Colors.brandSecondary,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Spacing.lg,
    paddingHorizontal: Spacing.xl,
    borderRadius: BorderRadius.lg,
    gap: Spacing.sm,
  },
  analyzeButtonDisabled: {
    opacity: 0.6,
  },
  analyzeButtonText: {
    color: Colors.brandForeground,
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
  },

  // Results Section
  resultsSection: {
    marginTop: 32,
  },
  resultsTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1a202c',
    marginBottom: 24,
    textAlign: 'center',
  },

  // Enhanced Scan Section
  scanSection: {
    marginBottom: 32,
    alignItems: 'center',
  },
  scanPlaceholder: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  cameraIconContainer: {
    width: 120,
    height: 120,
    borderRadius: 32,
    backgroundColor: 'rgba(107, 124, 90, 0.08)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
    borderWidth: 2,
    borderColor: 'rgba(107, 124, 90, 0.15)',
    position: 'relative',
  },

  readyText: {
    fontSize: 24,
    fontWeight: '600',
    color: '#1a202c',
    marginBottom: 8,
  },
  instructionText: {
    fontSize: 16,
    color: '#4a5568',
    textAlign: 'center',
    fontWeight: '500',
  },
  imageWrapper: {
    position: 'relative',
    alignItems: 'center',
  },
  previewImage: {
    width: 280,
    height: 280,
    borderRadius: 32,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 16,
  },
  removeButton: {
    position: 'absolute',
    top: 12,
    right: 12,
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },

  // Scanning Overlay
  scanningOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(107, 124, 90, 0.95)',
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scanningContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  scanningContent: {
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
  },
  scanningText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginTop: 16,
    textAlign: 'center',
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  progressBarContainer: {
    width: 200,
    height: 4,
    backgroundColor: '#E5E7EB',
    borderRadius: 2,
    marginBottom: 8,
  },
  progressBar: {
    height: '100%',
    backgroundColor: '#6B7C5A',
    borderRadius: 2,
  },
  progressText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '500',
  },

  // Responsive Nutrition Grid
  nutritionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 32,
    gap: isSmallScreen ? 8 : 12,
    paddingHorizontal: 4,
  },
  nutritionItem: {
    width: isSmallScreen ? '47%' : '48%',
    marginBottom: isSmallScreen ? 12 : 16,
    minHeight: isSmallScreen ? 100 : 120,
    maxWidth: isSmallScreen ? 160 : 180,
  },
  nutritionCard: {
    padding: 0,
    margin: 0,
  },
  nutritionCardContent: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    gap: 8,
    borderRadius: 32,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.1,
    shadowRadius: 16,
    elevation: 8,
    minHeight: 100,
    flex: 1,
  },
  nutritionIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  nutritionValue: {
    fontSize: isSmallScreen ? 20 : isMediumScreen ? 24 : 28,
    fontWeight: '700',
    color: '#1a202c',
    marginVertical: isSmallScreen ? 4 : 8,
    letterSpacing: -0.5,
    textAlign: 'center',
    flexShrink: 1,
  },
  nutritionLabel: {
    fontSize: isSmallScreen ? 12 : 14,
    color: '#4a5568',
    fontWeight: '500',
    textAlign: 'center',
    flexShrink: 1,
  },

  // Responsive Detected Foods Section
  detectedFoodsSection: {
    marginBottom: isSmallScreen ? 24 : 32,
  },
  detectedFoodsTitle: {
    fontSize: isSmallScreen ? 18 : 20,
    fontWeight: '600',
    color: '#1a202c',
    marginBottom: isSmallScreen ? 12 : 16,
    textAlign: 'center',
  },
  foodItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: isSmallScreen ? 6 : 8,
    gap: isSmallScreen ? 8 : 12,
    flexWrap: 'wrap',
  },
  foodItemText: {
    fontSize: isSmallScreen ? 14 : 16,
    color: '#2d3748',
    fontWeight: '500',
    flex: 1,
    flexShrink: 1,
  },

  // Responsive Recommendations Section
  recommendationsSection: {
    marginBottom: isSmallScreen ? 24 : 32,
  },
  recommendationsTitle: {
    fontSize: isSmallScreen ? 18 : 20,
    fontWeight: '600',
    color: '#1a202c',
    marginBottom: isSmallScreen ? 12 : 16,
    textAlign: 'center',
  },
  recommendation: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingVertical: isSmallScreen ? 6 : 8,
    gap: isSmallScreen ? 8 : 12,
    backgroundColor: 'rgba(107, 124, 90, 0.05)',
    borderRadius: 16,
    paddingHorizontal: isSmallScreen ? 10 : 12,
    marginBottom: isSmallScreen ? 6 : 8,
    maxWidth: '100%',
  },
  recommendationText: {
    fontSize: isSmallScreen ? 14 : 16,
    color: '#2d3748',
    fontWeight: '500',
    flex: 1,
    flexShrink: 1,
    lineHeight: isSmallScreen ? 20 : 24,
  },

  // Responsive Enhanced Food Item Styles
  enhancedFoodItem: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 24,
    padding: isSmallScreen ? 12 : 16,
    marginBottom: isSmallScreen ? 8 : 12,
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.1)',
    shadowColor: '#6B7C5A',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 8,
    maxWidth: '100%',
  },
  foodItemHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: isSmallScreen ? 6 : 8,
    gap: isSmallScreen ? 6 : 8,
    flexWrap: 'wrap',
  },
  foodItemName: {
    fontSize: isSmallScreen ? 14 : 16,
    fontWeight: '600',
    color: '#1a202c',
    flex: 1,
    flexShrink: 1,
    minWidth: 0,
  },
  foodItemPortion: {
    fontSize: isSmallScreen ? 12 : 14,
    color: '#6B7280',
    fontWeight: '500',
    flexShrink: 0,
  },
  foodItemNutrition: {
    flexDirection: isSmallScreen ? 'column' : 'row',
    justifyContent: 'space-between',
    alignItems: isSmallScreen ? 'flex-start' : 'center',
    marginBottom: isSmallScreen ? 6 : 8,
    gap: isSmallScreen ? 4 : 0,
  },
  foodItemCalories: {
    fontSize: isSmallScreen ? 12 : 14,
    fontWeight: '600',
    color: '#6B7C5A',
  },
  foodItemMacros: {
    fontSize: isSmallScreen ? 10 : 12,
    color: '#6B7280',
    flexShrink: 1,
  },
  confidenceBar: {
    height: 4,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    borderRadius: 2,
    overflow: 'hidden',
    position: 'relative',
  },
  confidenceFill: {
    height: '100%',
    backgroundColor: '#6B7C5A',
    borderRadius: 2,
  },
  confidenceText: {
    fontSize: 10,
    color: '#6B7280',
    marginTop: 4,
    textAlign: 'right',
  },

  // Health Score Styles
  healthScoreContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.1)',
  },
  healthScoreLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a202c',
    marginBottom: 8,
  },
  healthScoreBar: {
    height: 8,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 8,
  },
  healthScoreFill: {
    height: '100%',
    backgroundColor: '#6B7C5A',
    borderRadius: 4,
  },
  healthScoreValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6B7C5A',
    textAlign: 'center',
  },

  // Strength and Concern Styles
  strengthItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingVertical: isSmallScreen ? 6 : 8,
    gap: isSmallScreen ? 8 : 12,
    backgroundColor: 'rgba(76, 175, 80, 0.05)',
    borderRadius: 16,
    paddingHorizontal: isSmallScreen ? 10 : 12,
    marginBottom: isSmallScreen ? 6 : 8,
    maxWidth: '100%',
  },
  strengthText: {
    fontSize: isSmallScreen ? 14 : 16,
    color: '#2d3748',
    fontWeight: '500',
    flex: 1,
    flexShrink: 1,
    lineHeight: isSmallScreen ? 20 : 24,
  },
  concernItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingVertical: isSmallScreen ? 6 : 8,
    gap: isSmallScreen ? 8 : 12,
    backgroundColor: 'rgba(255, 152, 0, 0.05)',
    borderRadius: 16,
    paddingHorizontal: isSmallScreen ? 10 : 12,
    marginBottom: isSmallScreen ? 6 : 8,
    maxWidth: '100%',
  },
  concernText: {
    fontSize: isSmallScreen ? 14 : 16,
    color: '#2d3748',
    fontWeight: '500',
    flex: 1,
    flexShrink: 1,
    lineHeight: isSmallScreen ? 20 : 24,
  },



  // Modern Camera Button Styles
  modernCameraButton: {
    backgroundColor: Colors.brand,
    borderRadius: 32,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.25,
    shadowRadius: 24,
    elevation: 15,
  },
  cameraButtonContent: {
    paddingVertical: 24,
    paddingHorizontal: 32,
    backgroundColor: Colors.brand,
    borderRadius: 20,
    alignItems: 'center',
    gap: 8,
  },

  cameraButtonText: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.brandForeground,
    letterSpacing: -0.3,
  },
  cameraButtonSubtext: {
    fontSize: 12,
    fontWeight: '500',
    color: Colors.brandForeground,
    opacity: 0.8,
    letterSpacing: 0.5,
  },

  // Responsive Bottom Spacing
  bottomSpacing: {
    height: isSmallScreen ? 120 : 150,
  },

});

export default ScannerScreenModern;
